package forge;

import com.atlassian.bamboo.specs.api.BambooSpec;
import com.lmig.forge.bamboo.specs.patterns.AddOns;
import com.lmig.forge.bamboo.specs.patterns.build.rabbitmq.PcfRabbitMqBuild;
import com.lmig.forge.bamboo.specs.patterns.deployment.rabbitmq.PcfRabbitMqDeployment;

import static forge.PipelineParameters.PIPELINE_CONFIGURATION;

@BambooSpec
public class Pipeline {

    private static final AddOns ADD_ONS = new AddOns()
      .buildAddOns(
         /* Add an available build add-on here */
      )
      .deploymentAddOns(
         /* Add an available deployment add-on here */
      );

    public static void main(String[] args) {
      
      /**
       * BuildPattern: PcfRabbitMqBuild
       *
       * For additional information see https://docs.forge.lmig.com/articles/specs/patterns/build/rabbitmq/pcfrabbitmqbuild
       */
      new PcfRabbitMqBuild(PIPELINE_CONFIGURATION)
        .addOns(ADD_ONS)
        .publish();

      /**
       * DeploymentPattern: PcfRabbitMqDeployment
       *
       * For additional information see https://docs.forge.lmig.com/articles/specs/patterns/deployment/rabbitmq/pcfrabbitmqdeployment
       */
      new PcfRabbitMqDeployment(PIPELINE_CONFIGURATION)
        .addOns(ADD_ONS)
        .autoDeployAfterSuccessfulBuild()
        .publish();
    }
}
