package forge;

import com.lmig.forge.bamboo.specs.patterns.PipelineConfiguration;

public class PipelineParameters {
  /**
   * DO NOT MODIFY THIS CLASS. It will be regenerated systematically by the CloudForge Console.
   * If you want to add additional environments, please do so through the Console.
   *
   * Note: any changes made to plan or deployment permissions within your pipeline spec will be reset to match
   * what exist in the Console. To make any changes to permissions, please do so through the Console.
   *
   **/

  private static final String GENERATED_AT = "Thu, 20 Jul 2023 20:58:57 GMT";

  public static final PipelineConfiguration PIPELINE_CONFIGURATION = PipelineConfiguration.builder()
    .artifactGuid("7fde5e5a-47c3-4443-b399-93b6274daa8b")
    .build();
}
